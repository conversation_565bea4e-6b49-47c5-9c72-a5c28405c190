@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Enhanced Design System - Custom Properties */
:root {
  /* Typography System */
  --font-family-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-heading: 'Poppins', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;

  /* Font Weight Scale */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Font Size Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  --font-size-7xl: 4.5rem;    /* 72px */

  /* Line Height Scale */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Spacing Scale */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */

  /* Border Radius Scale */
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;

  /* Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  /* Comet Brand Colors */
  --color-comet-orange: #ff6b35;
  --color-comet-orange-light: #ff8a5b;
  --color-comet-orange-dark: #e55a2b;
  --color-comet-blue: #1e40af;
  --color-comet-blue-light: #3b82f6;
  --color-comet-blue-dark: #1e3a8a;

  /* Light Theme Colors */
  --color-background: #ffffff;
  --color-surface: #f8fafc;
  --color-surface-secondary: #f1f5f9;
  --color-surface-tertiary: #e2e8f0;
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-muted: #64748b;
  --color-border: #e2e8f0;
  --color-border-light: #f1f5f9;
  --color-border-strong: #cbd5e1;

  /* Semantic Colors - Light */
  --color-primary: var(--color-comet-orange);
  --color-primary-hover: var(--color-comet-orange-dark);
  --color-primary-light: #fff7ed;
  --color-secondary: var(--color-comet-blue);
  --color-secondary-hover: var(--color-comet-blue-dark);
  --color-secondary-light: #eff6ff;
  --color-accent: #8b5cf6;
  --color-accent-hover: #7c3aed;
  --color-accent-light: #f3f4f6;

  /* Status Colors */
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  --color-info: #06b6d4;
  --color-info-light: #cffafe;

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Enhanced Dark Theme */
[data-theme="dark"] {
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-surface-secondary: #334155;
  --color-surface-tertiary: #475569;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-muted: #94a3b8;
  --color-border: #334155;
  --color-border-light: #475569;
  --color-border-strong: #64748b;

  /* Semantic Colors - Dark */
  --color-primary: var(--color-comet-orange-light);
  --color-primary-hover: var(--color-comet-orange);
  --color-primary-light: #1e293b;
  --color-secondary: var(--color-comet-blue-light);
  --color-secondary-hover: var(--color-comet-blue);
  --color-secondary-light: #1e293b;
  --color-accent: #a78bfa;
  --color-accent-hover: #8b5cf6;
  --color-accent-light: #1e293b;

  /* Status Colors - Dark */
  --color-success-light: #1e293b;
  --color-warning-light: #1e293b;
  --color-error-light: #1e293b;
  --color-info-light: #1e293b;

  /* Enhanced shadows for dark theme */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* Global Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-heading {
  font-family: var(--font-family-heading);
}

/* Theme Transition */
*,
*::before,
*::after {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Enhanced Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-normal) ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  min-height: 44px; /* Accessibility: minimum touch target */
  position: relative;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  font-family: var(--font-family-sans);
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--color-primary);
}

.btn:disabled {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-hover) 0%, var(--color-primary) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-hover) 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-secondary-hover) 0%, var(--color-secondary) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-outline {
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
  background-color: transparent;
  backdrop-filter: blur(10px);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  height: 36px;
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-xs);
  min-height: 36px;
  border-radius: var(--radius-md);
}

.btn-md {
  height: 44px;
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-sm);
  min-height: 44px;
}

.btn-lg {
  height: 48px;
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
  min-height: 48px;
  border-radius: var(--radius-xl);
}

/* Surface Components */
.surface {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
}

.surface-secondary {
  background-color: var(--color-surface-secondary);
}

/* Text Utilities */
.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-muted {
  color: var(--color-text-muted);
}

/* Interactive Elements */
.interactive:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* Enhanced Focus States */
.focus-ring {
  outline: none;
}

.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-background), 0 0 0 4px var(--color-primary);
}

/* Enhanced Utility Classes */
.glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

[data-theme="dark"] .glass-effect {
  background-color: rgba(15, 23, 42, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.card-hover {
  transition: all var(--duration-normal) ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

/* Enhanced Typography Classes */
.text-display {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.text-body {
  font-family: var(--font-family-sans);
  line-height: var(--line-height-relaxed);
}

/* Standardized Spacing Utilities */
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.p-10 { padding: var(--space-10); }
.p-12 { padding: var(--space-12); }
.p-16 { padding: var(--space-16); }
.p-20 { padding: var(--space-20); }

.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-5 { padding-left: var(--space-5); padding-right: var(--space-5); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }

.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
.py-10 { padding-top: var(--space-10); padding-bottom: var(--space-10); }
.py-12 { padding-top: var(--space-12); padding-bottom: var(--space-12); }
.py-16 { padding-top: var(--space-16); padding-bottom: var(--space-16); }
.py-20 { padding-top: var(--space-20); padding-bottom: var(--space-20); }
.py-24 { padding-top: var(--space-24); padding-bottom: var(--space-24); }
.py-32 { padding-top: var(--space-32); padding-bottom: var(--space-32); }

/* Margin utilities */
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mx-auto { margin-left: auto; margin-right: auto; }

.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-12 { margin-bottom: var(--space-12); }
.mb-16 { margin-bottom: var(--space-16); }

.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }
.mt-12 { margin-top: var(--space-12); }

/* Gap utilities */
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Alignment utilities */
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Flex utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth Page Transitions */
.page-transition {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.page-transition.active {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced Visual Effects */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-secondary), var(--color-accent));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) ease;
}

.glow-effect:hover::before {
  opacity: 0.7;
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  0% { transform: scale(1); opacity: 0.7; }
  100% { transform: scale(1.05); opacity: 0.9; }
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-hover) 100%);
}

.bg-gradient-brand {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
}

.bg-gradient-surface {
  background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-secondary) 100%);
}

/* Enhanced Hover States */
.hover-lift {
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Modern Card Styles */
.card-modern {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) ease;
  position: relative;
  overflow: hidden;
}

.card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
  opacity: 0;
  transition: opacity var(--duration-normal) ease;
}

.card-modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.card-modern:hover::before {
  opacity: 1;
}

/* Responsive Design Enhancements */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1536px;
  }
}

/* Mobile-first responsive spacing */
.section-padding {
  padding-top: var(--space-12);
  padding-bottom: var(--space-12);
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: var(--space-16);
    padding-bottom: var(--space-16);
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: var(--space-20);
    padding-bottom: var(--space-20);
  }
}

/* Responsive padding utilities */
.section-padding-sm {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

@media (min-width: 768px) {
  .section-padding-sm {
    padding-top: var(--space-12);
    padding-bottom: var(--space-12);
  }
}

.section-padding-lg {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}

@media (min-width: 768px) {
  .section-padding-lg {
    padding-top: var(--space-24);
    padding-bottom: var(--space-24);
  }
}

@media (min-width: 1024px) {
  .section-padding-lg {
    padding-top: var(--space-32);
    padding-bottom: var(--space-32);
  }
}

/* Responsive text sizes */
.text-responsive-sm {
  font-size: var(--font-size-sm);
}

.text-responsive-base {
  font-size: var(--font-size-base);
}

.text-responsive-lg {
  font-size: var(--font-size-lg);
}

.text-responsive-xl {
  font-size: var(--font-size-xl);
}

.text-responsive-2xl {
  font-size: var(--font-size-2xl);
}

@media (min-width: 768px) {
  .text-responsive-sm { font-size: var(--font-size-base); }
  .text-responsive-base { font-size: var(--font-size-lg); }
  .text-responsive-lg { font-size: var(--font-size-xl); }
  .text-responsive-xl { font-size: var(--font-size-2xl); }
  .text-responsive-2xl { font-size: var(--font-size-3xl); }
}

@media (min-width: 1024px) {
  .text-responsive-sm { font-size: var(--font-size-lg); }
  .text-responsive-base { font-size: var(--font-size-xl); }
  .text-responsive-lg { font-size: var(--font-size-2xl); }
  .text-responsive-xl { font-size: var(--font-size-3xl); }
  .text-responsive-2xl { font-size: var(--font-size-4xl); }
}

/* Mobile optimizations */
@media (max-width: 767px) {
  .card-modern {
    border-radius: var(--radius-xl);
  }

  .btn {
    min-height: 48px; /* Larger touch targets on mobile */
  }

  .hover-lift:hover,
  .hover-scale:hover {
    transform: none; /* Disable hover effects on touch devices */
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover,
  .hover-scale:hover,
  .card-hover:hover {
    transform: none;
  }

  .glow-effect:hover::before {
    opacity: 0;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .float-animation,
  .glow-effect::before {
    animation: none !important;
  }

  .hover-lift,
  .hover-scale,
  .card-hover {
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-outline {
    border-width: 3px;
  }

  .card-modern {
    border-width: 2px;
  }
}

/* Modern CSS Enhancements */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Better focus management */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Modern grid layouts */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Enhanced text alignment */
.text-balance {
  text-wrap: balance;
}

/* Better image handling */
img {
  max-width: 100%;
  height: auto;
}

.img-cover {
  object-fit: cover;
  object-position: center;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-strong);
}

/* Scrollbar Hide Utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Smooth scroll behavior */
.scroll-smooth {
  scroll-behavior: smooth;
}
