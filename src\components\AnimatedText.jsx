import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const AnimatedText = ({
  text,
  delay = 0.1,
  className = '',
  animationType = 'fade-in',
  staggerChildren = 0.05,
  ...props
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (animationType === 'typewriter') {
      const timer = setTimeout(() => {
        if (currentIndex < text.length) {
          setDisplayedText(prev => prev + text[currentIndex]);
          setCurrentIndex(prev => prev + 1);
        }
      }, delay * 1000);

      return () => clearTimeout(timer);
    } else {
      setDisplayedText(text);
    }
  }, [currentIndex, text, delay, animationType]);

  const getAnimationVariants = () => {
    switch (animationType) {
      case 'fade-in':
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration: 0.6, delay } }
        };
      case 'slide-up':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0, transition: { duration: 0.6, delay } }
        };
      case 'slide-down':
        return {
          hidden: { opacity: 0, y: -20 },
          visible: { opacity: 1, y: 0, transition: { duration: 0.6, delay } }
        };
      case 'slide-left':
        return {
          hidden: { opacity: 0, x: 20 },
          visible: { opacity: 1, x: 0, transition: { duration: 0.6, delay } }
        };
      case 'slide-right':
        return {
          hidden: { opacity: 0, x: -20 },
          visible: { opacity: 1, x: 0, transition: { duration: 0.6, delay } }
        };
      case 'scale-in':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { opacity: 1, scale: 1, transition: { duration: 0.6, delay } }
        };
      case 'stagger':
        return {
          hidden: {},
          visible: {
            transition: {
              staggerChildren,
              delayChildren: delay
            }
          }
        };
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration: 0.6, delay } }
        };
    }
  };

  if (animationType === 'typewriter') {
    return (
      <div className={`${className}`} {...props}>
        {displayedText}
        {currentIndex < text.length && (
          <motion.span
            animate={{ opacity: [1, 0] }}
            transition={{ duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
            className="ml-1"
          >
            |
          </motion.span>
        )}
      </div>
    );
  }

  if (animationType === 'stagger') {
    return (
      <motion.div
        className={className}
        variants={getAnimationVariants()}
        initial="hidden"
        animate="visible"
        {...props}
      >
        {text.split('').map((char, index) => (
          <motion.span
            key={index}
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: { opacity: 1, y: 0 }
            }}
            className={char === ' ' ? 'inline-block w-2' : 'inline-block'}
          >
            {char}
          </motion.span>
        ))}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={className}
      variants={getAnimationVariants()}
      initial="hidden"
      animate="visible"
      {...props}
    >
      {text}
    </motion.div>
  );
};

export default AnimatedText;
