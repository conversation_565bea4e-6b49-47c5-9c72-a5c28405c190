import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import AnimatedText from '../components/AnimatedText';
import Button from '../components/Button';
import { useProducts } from '../contexts/ProductContext';
import { useTheme } from '../contexts/ThemeContext';

const Home = () => {
  const { products } = useProducts();
  const { isDark } = useTheme();

  // Carousel state
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const scrollContainerRef = useRef(null);
  const autoScrollTimerRef = useRef(null);

  // Carousel configuration
  const ITEMS_PER_VIEW = 4; // Number of thumbnails visible at once
  const AUTO_SCROLL_INTERVAL = 4000; // 4 seconds
  const totalSlides = Math.ceil(products.length / ITEMS_PER_VIEW);

  // Auto-scroll functionality
  const startAutoScroll = () => {
    if (autoScrollTimerRef.current) {
      clearInterval(autoScrollTimerRef.current);
    }

    autoScrollTimerRef.current = setInterval(() => {
      if (!isHovered && !isPaused) {
        setCurrentIndex(prevIndex => {
          const nextIndex = (prevIndex + 1) % totalSlides;
          scrollToIndex(nextIndex);
          return nextIndex;
        });
      }
    }, AUTO_SCROLL_INTERVAL);
  };

  const stopAutoScroll = () => {
    if (autoScrollTimerRef.current) {
      clearInterval(autoScrollTimerRef.current);
      autoScrollTimerRef.current = null;
    }
  };

  const scrollToIndex = (index) => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const itemWidth = container.scrollWidth / products.length;
      const scrollPosition = index * ITEMS_PER_VIEW * itemWidth;

      container.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  };

  const handleThumbnailClick = (productIndex) => {
    const slideIndex = Math.floor(productIndex / ITEMS_PER_VIEW);
    setCurrentIndex(slideIndex);
    scrollToIndex(slideIndex);
    setIsPaused(true);

    // Resume auto-scroll after 5 seconds of inactivity
    setTimeout(() => {
      setIsPaused(false);
    }, 5000);
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Initialize auto-scroll on component mount
  useEffect(() => {
    if (products.length > ITEMS_PER_VIEW) {
      startAutoScroll();
    }

    return () => {
      stopAutoScroll();
    };
  }, [products.length, isHovered, isPaused, totalSlides]);

  // Restart auto-scroll when hover state changes
  useEffect(() => {
    if (products.length > ITEMS_PER_VIEW) {
      startAutoScroll();
    }
  }, [isHovered, isPaused]);

  const features = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      ),
      title: 'Stellar Craftsmanship',
      description: 'From the sole to the final shoe, exceptional attention to detail in every pair with premium materials and innovative design.'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: 'Never Shy, Never Sorry',
      description: 'Bold designs that break away from the ordinary to create something extraordinary. Embrace your individuality with confidence.'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
        </svg>
      ),
      title: 'Premium Quality',
      description: 'Exceptional materials and captivating concepts for the perfect balance of comfort, durability, and style in every step.'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className={`relative section-padding overflow-hidden ${
        isDark
          ? 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900'
          : 'bg-gradient-to-br from-orange-50 via-white to-red-50'
      }`}>
        <div className="container-responsive relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <AnimatedText
                text="Welcome to COMET"
                animationType="fade-in"
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-heading text-primary mb-4 sm:mb-6"
                delay={0.2}
              />
              <AnimatedText
                text="Never Shy, Never Sorry"
                animationType="slide-up"
                className="text-lg sm:text-xl md:text-2xl font-medium text-secondary mb-6 sm:mb-8"
                delay={0.4}
              />
            </motion.div>

            <motion.p
              className="text-lg sm:text-xl text-secondary max-w-3xl mx-auto leading-relaxed mb-8 sm:mb-12 px-4 sm:px-0"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Discover our revolutionary collection of lifestyle sneakers that blend stellar craftsmanship with captivating design.
              Because ordinary is boring.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4 sm:px-0"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <Link to="/products" className="btn btn-primary btn-lg w-full sm:w-auto">
                Shop Collection
              </Link>
              <Link to="/about" className="btn btn-outline btn-lg w-full sm:w-auto">
                Our Story
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div 
            className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-full blur-xl"
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div 
            className="absolute bottom-20 right-10 w-48 h-48 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
            animate={{ 
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{ 
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="section-padding surface">
        <div className="container-responsive">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold font-heading text-primary mb-4">
              Our Collections
            </h2>
            <p className="text-lg sm:text-xl text-secondary max-w-2xl mx-auto px-4 sm:px-0">
              Discover our most popular sneakers that embody the spirit of Never Shy, Never Sorry
            </p>
          </motion.div>

          {/* Main Hero Banner */}
          <motion.div
            className="relative h-[70vh] md:h-[80vh] rounded-2xl overflow-hidden group cursor-pointer"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.01 }}
          >
            <div className="absolute inset-0">
              <img
                src={products[0]?.images[0] || "https://www.wearcomet.com/cdn/shop/files/lateral_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=1752230759&width=533"}
                alt={products[0]?.name || "Featured Product"}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                onError={(e) => {
                  e.target.src = 'https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent"></div>
            </div>

            {/* Top-left badge */}
            <span className="absolute top-6 left-6 bg-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
              Our Brands
            </span>

            {/* Top-right badge */}
            <span className="absolute top-6 right-6 bg-red-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
              {products[0]?.isNew ? 'New Launch' : products[0]?.isBestSeller ? 'Bestseller' : 'Featured'}
            </span>

            {/* Content - Left side */}
            <div className="relative z-10 h-full flex flex-col justify-center px-8 md:px-12">
              <div className="max-w-2xl">
                <h3 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-4 leading-tight">
                  {products[0]?.name || 'ALTER ROGUE'}
                </h3>
                <p className="text-white/90 text-xl md:text-2xl mb-8 leading-relaxed max-w-lg">
                  {products[0]?.description || 'Bold and edgy sneakers that dare to be different. Experience the perfect blend of street style and premium comfort.'}
                </p>

                {/* Bottom-left button */}
                <div className="absolute bottom-8 left-8 md:left-12">
                  <Link
                    to="/products"
                    className="inline-flex items-center bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors duration-200 shadow-lg"
                  >
                    View All
                    <svg className="ml-2 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Thumbnail Strip with Auto-Carousel */}
          <motion.div
            className="mt-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {/* Carousel Container */}
            <div className="relative">
              <div
                ref={scrollContainerRef}
                className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide scroll-smooth"
                style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              >
                {products.map((product, index) => (
                  <motion.div
                    key={product.id}
                    className="flex-shrink-0 group cursor-pointer"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: (index % 8) * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.05 }}
                    onClick={() => handleThumbnailClick(index)}
                  >
                    <Link to={`/products/${product.id}`}>
                      <div className="relative w-24 h-24 md:w-32 md:h-32 lg:w-36 lg:h-36 rounded-xl overflow-hidden bg-gray-100 dark:bg-slate-700 shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <img
                          src={product.images[0]}
                          alt={product.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80';
                          }}
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>

                        {/* Product info overlay on hover */}
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                          <p className="text-white text-xs font-medium truncate">{product.name}</p>
                          <p className="text-white/80 text-xs">₹{product.price.toLocaleString()}</p>
                        </div>

                        {/* Active indicator */}
                        <div className={`absolute top-2 right-2 w-2 h-2 rounded-full transition-all duration-300 ${
                          Math.floor(index / ITEMS_PER_VIEW) === currentIndex
                            ? 'bg-orange-500 scale-100'
                            : 'bg-white/50 scale-75'
                        }`}></div>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* Progress Indicators */}
              {totalSlides > 1 && (
                <div className="flex justify-center mt-4 gap-2">
                  {Array.from({ length: totalSlides }).map((_, index) => (
                    <button
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index === currentIndex
                          ? 'bg-orange-500 w-8'
                          : 'bg-gray-300 dark:bg-slate-600 hover:bg-orange-300'
                      }`}
                      onClick={() => {
                        setCurrentIndex(index);
                        scrollToIndex(index);
                        setIsPaused(true);
                        setTimeout(() => setIsPaused(false), 5000);
                      }}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>
              )}

              {/* Auto-scroll status indicator */}
              <div className="absolute top-0 right-0 flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                {isHovered && (
                  <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full">
                    Paused
                  </span>
                )}
                {isPaused && !isHovered && (
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                    Manual
                  </span>
                )}
              </div>
            </div>
          </motion.div>


        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding surface-secondary">
        <div className="container-responsive">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold font-heading text-primary mb-4">
              Why Choose Comet?
            </h2>
            <p className="text-lg sm:text-xl text-secondary max-w-2xl mx-auto px-4 sm:px-0">
              Experience the perfect fusion of innovation, style, and premium craftsmanship
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="group card-modern hover-lift p-8"
                variants={itemVariants}
              >
                <div className="w-16 h-16 bg-gradient-brand rounded-2xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl glow-effect">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-primary mb-4 group-hover:text-orange-500 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-secondary leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className="section-padding relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500"></div>
        <div className="absolute inset-0 bg-black/20"></div>

        <div className="container-responsive relative z-10">
          <motion.div
            className="text-center text-white"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold font-heading mb-6">
              Good Shoes, Good Places
            </h2>
            <p className="text-lg sm:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90 px-4 sm:px-0">
              Founded by Utkarsh & Dishant, Comet represents the perfect blend of innovation and craftsmanship.
              Our "Never Shy, Never Sorry" philosophy drives us to create sneakers that empower your unique style.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4 sm:px-0">
              <Link to="/products" className="btn bg-white text-orange-500 hover:bg-gray-100 btn-lg w-full sm:w-auto">
                Shop Collection
              </Link>
              <Link to="/about" className="btn btn-outline border-white text-white hover:bg-white hover:text-orange-500 btn-lg w-full sm:w-auto">
                Our Story
              </Link>
            </div>
          </motion.div>
        </div>
        
        {/* Floating Elements */}
        <motion.div 
          className="absolute top-10 left-10 w-24 h-24 border-2 border-white/30 rounded-full"
          animate={{ 
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div 
          className="absolute bottom-10 right-10 w-16 h-16 border-2 border-white/30 rounded-full"
          animate={{ 
            rotate: [360, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </section>

      {/* Newsletter Section */}
      <section className="section-padding surface">
        <div className="container-responsive">
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold font-heading text-primary mb-4">
              Stay in the Loop
            </h2>
            <p className="text-lg sm:text-xl text-secondary mb-8 px-4 sm:px-0">
              Be the first to know about new releases, exclusive offers, and Comet community updates.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto px-4 sm:px-0">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-primary focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
              <button className="btn btn-primary btn-md whitespace-nowrap w-full sm:w-auto">
                Subscribe
              </button>
            </div>
            
            <p className="text-sm text-muted mt-4">
              No spam, unsubscribe at any time. Read our privacy policy.
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
