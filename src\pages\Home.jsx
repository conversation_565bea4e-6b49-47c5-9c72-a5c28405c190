import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import AnimatedText from '../components/AnimatedText';
import Button from '../components/Button';
import { useProducts } from '../contexts/ProductContext';
import { useTheme } from '../contexts/ThemeContext';

const Home = () => {
  const { products } = useProducts();
  const { isDark } = useTheme();
  
  const featuredProducts = products.filter(product => product.isBestSeller).slice(0, 3);
  
  const features = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      ),
      title: 'Stellar Craftsmanship',
      description: 'From the sole to the final shoe, exceptional attention to detail in every pair with premium materials and innovative design.'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: 'Never Shy, Never Sorry',
      description: 'Bold designs that break away from the ordinary to create something extraordinary. Embrace your individuality with confidence.'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
        </svg>
      ),
      title: 'Premium Quality',
      description: 'Exceptional materials and captivating concepts for the perfect balance of comfort, durability, and style in every step.'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className={`relative section-padding overflow-hidden ${
        isDark
          ? 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900'
          : 'bg-gradient-to-br from-orange-50 via-white to-red-50'
      }`}>
        <div className="container-responsive relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <AnimatedText
                text="Welcome to COMET"
                animationType="fade-in"
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-heading text-primary mb-4 sm:mb-6"
                delay={0.2}
              />
              <AnimatedText
                text="Never Shy, Never Sorry"
                animationType="slide-up"
                className="text-lg sm:text-xl md:text-2xl font-medium text-secondary mb-6 sm:mb-8"
                delay={0.4}
              />
            </motion.div>

            <motion.p
              className="text-lg sm:text-xl text-secondary max-w-3xl mx-auto leading-relaxed mb-8 sm:mb-12 px-4 sm:px-0"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Discover our revolutionary collection of lifestyle sneakers that blend stellar craftsmanship with captivating design.
              Because ordinary is boring.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4 sm:px-0"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <Link to="/products" className="btn btn-primary btn-lg w-full sm:w-auto">
                Shop Collection
              </Link>
              <Link to="/about" className="btn btn-outline btn-lg w-full sm:w-auto">
                Our Story
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div 
            className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-full blur-xl"
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div 
            className="absolute bottom-20 right-10 w-48 h-48 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
            animate={{ 
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{ 
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="section-padding surface">
        <div className="container-responsive">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold font-heading text-primary mb-4">
              Best Sellers
            </h2>
            <p className="text-lg sm:text-xl text-secondary max-w-2xl mx-auto px-4 sm:px-0">
              Discover why these sneakers are loved by thousands of customers worldwide
            </p>
          </motion.div>

          {/* Main Featured Product Banner */}
          <motion.div
            className="relative h-96 md:h-[500px] rounded-2xl overflow-hidden group cursor-pointer"
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0">
              <img
                src={featuredProducts[0]?.images[0]}
                alt={featuredProducts[0]?.name}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent"></div>
            </div>

            <div className="relative z-10 h-full flex flex-col justify-center px-8 md:px-12">
              <div className="max-w-lg">
                <span className="inline-block bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                  Our Brands
                </span>
                <h3 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                  {featuredProducts[0]?.name}
                </h3>
                <p className="text-white/90 text-lg mb-6 leading-relaxed">
                  {featuredProducts[0]?.description}
                </p>
                <div className="flex items-center gap-4 mb-6">
                  <span className="text-2xl md:text-3xl font-bold text-white">
                    ₹{featuredProducts[0]?.price.toLocaleString()}
                  </span>
                  {featuredProducts[0]?.originalPrice > featuredProducts[0]?.price && (
                    <span className="text-lg text-white/70 line-through">
                      ₹{featuredProducts[0]?.originalPrice.toLocaleString()}
                    </span>
                  )}
                </div>
                <Link
                  to={`/products/${featuredProducts[0]?.id}`}
                  className="inline-flex items-center bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200"
                >
                  View All
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>

            {featuredProducts[0]?.isBestSeller && (
              <span className="absolute top-6 right-6 bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                Our Brands
              </span>
            )}
          </motion.div>

          {/* Product Thumbnails */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {featuredProducts.slice(0, 6).map((product, index) => (
              <motion.div
                key={product.id}
                className="group relative aspect-square rounded-xl overflow-hidden cursor-pointer"
                variants={itemVariants}
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <img
                  src={product.images[0]}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-0 left-0 right-0 p-3 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                  <h4 className="font-medium text-sm truncate">{product.name}</h4>
                  <p className="text-xs text-white/80">{product.collection}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Link to="/products" className="btn btn-outline btn-lg">
              View All Products
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding surface-secondary">
        <div className="container-responsive">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold font-heading text-primary mb-4">
              Why Choose Comet?
            </h2>
            <p className="text-lg sm:text-xl text-secondary max-w-2xl mx-auto px-4 sm:px-0">
              Experience the perfect fusion of innovation, style, and premium craftsmanship
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="group card-modern hover-lift p-8"
                variants={itemVariants}
              >
                <div className="w-16 h-16 bg-gradient-brand rounded-2xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl glow-effect">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-primary mb-4 group-hover:text-orange-500 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-secondary leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className="section-padding relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500"></div>
        <div className="absolute inset-0 bg-black/20"></div>

        <div className="container-responsive relative z-10">
          <motion.div
            className="text-center text-white"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold font-heading mb-6">
              Good Shoes, Good Places
            </h2>
            <p className="text-lg sm:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90 px-4 sm:px-0">
              Founded by Utkarsh & Dishant, Comet represents the perfect blend of innovation and craftsmanship.
              Our "Never Shy, Never Sorry" philosophy drives us to create sneakers that empower your unique style.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4 sm:px-0">
              <Link to="/products" className="btn bg-white text-orange-500 hover:bg-gray-100 btn-lg w-full sm:w-auto">
                Shop Collection
              </Link>
              <Link to="/about" className="btn btn-outline border-white text-white hover:bg-white hover:text-orange-500 btn-lg w-full sm:w-auto">
                Our Story
              </Link>
            </div>
          </motion.div>
        </div>
        
        {/* Floating Elements */}
        <motion.div 
          className="absolute top-10 left-10 w-24 h-24 border-2 border-white/30 rounded-full"
          animate={{ 
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div 
          className="absolute bottom-10 right-10 w-16 h-16 border-2 border-white/30 rounded-full"
          animate={{ 
            rotate: [360, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </section>

      {/* Newsletter Section */}
      <section className="section-padding surface">
        <div className="container-responsive">
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold font-heading text-primary mb-4">
              Stay in the Loop
            </h2>
            <p className="text-lg sm:text-xl text-secondary mb-8 px-4 sm:px-0">
              Be the first to know about new releases, exclusive offers, and Comet community updates.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto px-4 sm:px-0">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-primary focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
              <button className="btn btn-primary btn-md whitespace-nowrap w-full sm:w-auto">
                Subscribe
              </button>
            </div>
            
            <p className="text-sm text-muted mt-4">
              No spam, unsubscribe at any time. Read our privacy policy.
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
