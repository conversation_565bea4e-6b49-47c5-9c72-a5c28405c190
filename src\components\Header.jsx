import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import ThemeToggle from './ThemeToggle';
import { useTheme } from '../contexts/ThemeContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { isDark } = useTheme();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Products', path: '/products' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  return (
    <motion.header
      className={`sticky top-0 z-50 transition-all duration-300 ${
        isScrolled
          ? `glass-effect shadow-xl border-b border-white/10`
          : `${isDark ? 'bg-slate-900' : 'bg-white'} shadow-sm border-b border-transparent`
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container-responsive">
        <div className="flex justify-between items-center h-16 lg:h-20">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 group">
            <motion.div
              className="flex items-center gap-2 sm:gap-3"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                <span className="text-white font-bold text-lg sm:text-xl group-hover:scale-110 transition-transform duration-300">C</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl sm:text-2xl font-heading font-bold gradient-text group-hover:scale-105 transition-transform duration-300">
                  COMET
                </h1>
                <p className="text-xs text-muted font-medium -mt-1 group-hover:text-orange-400 transition-colors duration-300">Never Shy, Never Sorry</p>
              </div>
              {/* Mobile-only simplified logo */}
              <div className="block sm:hidden">
                <h1 className="text-xl font-heading font-bold gradient-text group-hover:scale-105 transition-transform duration-300">
                  COMET
                </h1>
              </div>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-2">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="relative group"
              >
                <motion.div
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    location.pathname === item.path
                      ? 'text-primary bg-orange-50 dark:bg-orange-500/10'
                      : 'text-secondary hover:text-primary hover:bg-gray-50 dark:hover:bg-slate-800'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.name}
                  {location.pathname === item.path && (
                    <motion.div
                      className="absolute bottom-0 left-4 right-4 h-0.5 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"
                      layoutId="activeNav"
                      transition={{ type: "spring", stiffness: 380, damping: 30 }}
                    />
                  )}
                </motion.div>
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center gap-3">
            <ThemeToggle />

            <motion.div className="flex items-center gap-2">
              <Link
                to="/login"
                className="btn btn-ghost btn-sm"
              >
                Login
              </Link>
              <Link
                to="/signup"
                className="btn btn-primary btn-sm"
              >
                Sign Up
              </Link>
            </motion.div>
          </div>

          {/* Mobile Actions */}
          <div className="lg:hidden flex items-center gap-2 sm:gap-3">
            <ThemeToggle className="scale-75 sm:scale-90" />

            {/* Mobile Menu Button */}
            <motion.button
              className="relative w-9 h-9 sm:w-10 sm:h-10 flex items-center justify-center rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
              onClick={toggleMenu}
              whileTap={{ scale: 0.95 }}
              aria-label="Toggle menu"
              aria-expanded={isMenuOpen}
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center">
                <motion.span
                  className="bg-current block h-0.5 w-6 rounded-sm"
                  animate={{
                    rotate: isMenuOpen ? 45 : 0,
                    y: isMenuOpen ? 6 : -3
                  }}
                  transition={{ duration: 0.2 }}
                />
                <motion.span
                  className="bg-current block h-0.5 w-6 rounded-sm mt-1"
                  animate={{
                    opacity: isMenuOpen ? 0 : 1,
                    x: isMenuOpen ? -10 : 0
                  }}
                  transition={{ duration: 0.2 }}
                />
                <motion.span
                  className="bg-current block h-0.5 w-6 rounded-sm mt-1"
                  animate={{
                    rotate: isMenuOpen ? -45 : 0,
                    y: isMenuOpen ? -6 : 3
                  }}
                  transition={{ duration: 0.2 }}
                />
              </div>
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="lg:hidden absolute left-0 right-0 top-full bg-white/95 dark:bg-slate-900/95 backdrop-blur-md border-t border-gray-200 dark:border-slate-700 shadow-xl"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
            >
              <div className="container-responsive py-6">
                <nav className="space-y-1">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        to={item.path}
                        className={`block px-4 py-3 text-base font-medium rounded-xl transition-all duration-200 ${
                          location.pathname === item.path
                            ? 'text-primary bg-orange-50 dark:bg-orange-500/10 border-l-4 border-orange-500'
                            : 'text-secondary hover:text-primary hover:bg-gray-50 dark:hover:bg-slate-800'
                        }`}
                      >
                        {item.name}
                      </Link>
                    </motion.div>
                  ))}
                </nav>
                
                <motion.div 
                  className="mt-6 pt-6 border-t border-gray-200 dark:border-slate-700 space-y-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <Link
                    to="/login"
                    className="block w-full btn btn-ghost btn-md text-left justify-start"
                  >
                    Login to Account
                  </Link>
                  <Link
                    to="/signup"
                    className="block w-full btn btn-primary btn-md"
                  >
                    Create Account
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

export default Header;
